# 哔哩下载姬

<p align="center">
    <a href="https://github.com/leiurayer/downkyi/stargazers" style="text-decoration:none" >
        <img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/leiurayer/downkyi">
    </a>
    <a href="https://github.com/leiurayer/downkyi/network" style="text-decoration:none" >
        <img alt="GitHub forks" src="https://img.shields.io/github/forks/leiurayer/downkyi">
    </a>
    <a href="https://github.com/leiurayer/downkyi/issues" style="text-decoration:none">
        <img alt="GitHub issues" src="https://img.shields.io/github/issues/leiurayer/downkyi">
    </a>
    <a href="https://github.com/leiurayer/downkyi/blob/main/LICENSE" style="text-decoration:none" >
        <img alt="GitHub" src="https://img.shields.io/github/license/leiurayer/downkyi">
    </a>
</p>

![index.png](https://s2.loli.net/2022/06/04/dOsqtfBXceRgrj2.png)

哔哩下载姬（DownKyi）是一个简单易用的哔哩哔哩视频下载工具，具有简洁的界面，流畅的操作逻辑。哔哩下载姬可以下载几乎所有的B站视频，并输出mp4格式的文件；采用Aria下载器多线程下载，采用FFmpeg对视频进行混流、提取音视频等操作。

[更多详情](src/README.md)

## 下载

<p align="left">
    <a href="https://github.com/leiurayer/downkyi/releases/latest" style="text-decoration:none">
       <img alt="GitHub release (latest by date)" src="https://img.shields.io/github/v/release/leiurayer/downkyi">
    </a>
    <a href="https://github.com/leiurayer/downkyi/releases/latest" style="text-decoration:none">
       <img alt="GitHub Release Date" src="https://img.shields.io/github/release-date/leiurayer/downkyi">
    </a>
    <a href="https://github.com/leiurayer/downkyi/releases" style="text-decoration:none">
       <img alt="GitHub all releases" src="https://img.shields.io/github/downloads/leiurayer/downkyi/total">
    </a>
</p>

[更新日志](CHANGELOG.md)

## 问题

- Aria下载失败：检查aria2c.exe是否可以正常工作和是否允许通过防火墙；或者尝试切换端口号。
- 内建下载器失败：请提issue，也欢迎pr。
- 下载时卡在“混流中”：检查ffmpeg.exe是否可以正常工作。
- 去水印：宽/高为水印的尺寸，X/Y为水印在图像中的位置（以左上角为原点），这四个数据可通过Photoshop获得。

## 赞助

如果这个项目对您有很大帮助，并且您希望支持该项目的开发和维护，请随时扫描一下二维码进行捐赠。非常感谢您的捐款，谢谢！

![Alipay.png](https://s2.loli.net/2022/06/04/6LpfinSa5FoZmNB.png)![WeChat.png](https://s2.loli.net/2022/06/04/2yotOSvwmahPdXU.png)

## 开发

### x86 & x64

发布的压缩包中aria2c.exe和ffmpeg.exe均为32位，如果需要请用下面链接中的文件替换。

- [aria2-1.36.0-win-32bit](third_party/aria2-1.36.0-win-32bit-build1.zip)
- [aria2-1.36.0-win-64bit](third_party/aria2-1.36.0-win-64bit-build1.zip)
- [FFmpeg](https://github.com/leiurayer/FFmpeg-Builds/releases/tag/latest)

### 相关项目

- [哔哩哔哩-API收集整理](https://github.com/SocialSisterYi/bilibili-API-collect)：B站API归档
- [Prism](https://github.com/PrismLibrary/Prism)：MVVM框架
- [WebPSharp](https://github.com/leiurayer/WebPSharp)：WebP格式图片支持，[NuGet程序包](third_party/WebPSharp.0.5.1.nupkg)

## 免责申明

1. 本软件只提供视频解析，不提供任何资源上传、存储到服务器的功能。
2. 本软件仅解析来自B站的内容，不会对解析到的音视频进行二次编码，部分视频会进行有限的格式转换、拼接等操作。
3. 本软件解析得到的所有内容均来自B站UP主上传、分享，其版权均归原作者所有。内容提供者、上传者（UP主）应对其提供、上传的内容承担全部责任。
4. **本软件提供的所有内容，仅可用作学习交流使用，未经原作者授权，禁止用于其他用途。请在下载24小时内删除。为尊重作者版权，请前往资源的原始发布网站观看，支持原创，谢谢。**
5. 因使用本软件产生的版权问题，软件作者概不负责。
