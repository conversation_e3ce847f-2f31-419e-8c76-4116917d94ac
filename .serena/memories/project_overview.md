# DownKyi 项目概览

## 项目目的
哔哩下载姬（DownKyi）是一个简单易用的哔哩哔哩视频下载工具，具有简洁的界面，流畅的操作逻辑。主要功能：
- 下载几乎所有的B站视频，输出mp4格式文件
- 采用Aria下载器多线程下载
- 采用FFmpeg对视频进行混流、提取音视频等操作
- 支持去水印功能

## 技术栈
- **框架**: .NET 8.0
- **UI框架**: Avalonia UI 11.2.3 (跨平台桌面应用框架)
- **架构模式**: MVVM (Model-View-ViewModel)
- **MVVM框架**: CommunityToolkit.Mvvm 8.4.0
- **依赖注入**: Microsoft.Extensions.DependencyInjection 9.0.1
- **数据库**: SQLite (sqlite-net-sqlcipher)
- **日志**: NLog 5.4.0
- **下载器**: Aria2cNet 1.0.1
- **B站API**: Downkyi.BiliSharp 0.0.4

## 项目结构
```
src/
├── Downkyi/           # 主应用程序项目 (WinExe)
├── Downkyi.UI/        # UI层和ViewModels
├── Downkyi.Core/      # 核心业务逻辑
└── Downkyi.sln        # 解决方案文件
```

## 外部依赖
- aria2c.exe (下载器)
- ffmpeg.exe (视频处理)
- 支持32位和64位版本