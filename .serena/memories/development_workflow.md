# 开发工作流程

## 项目开发流程

### 1. 环境准备
```powershell
# 确保.NET SDK可用
$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
dotnet --version
```

### 2. 项目构建
```powershell
# 进入源码目录
cd src

# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行应用
dotnet run --project Downkyi
```

### 3. 代码修改流程
1. **分析需求** - 确定要修改的功能模块
2. **定位代码** - 使用Serena的符号查找功能
3. **理解架构** - 查看相关的服务和模型
4. **实施修改** - 遵循MVVM模式和项目约定
5. **测试验证** - 构建并运行测试

### 4. 常见开发任务

#### 添加新的UI功能
1. 在`Downkyi.UI/Models`中创建数据模型
2. 在`Downkyi.UI/ViewModels`中创建视图模型
3. 在`Downkyi/Views`中创建AXAML视图
4. 注册服务和依赖注入

#### 添加新的业务逻辑
1. 在`Downkyi.Core`中实现核心逻辑
2. 定义接口和实现类
3. 更新设置管理器（如需要）
4. 添加相应的工具类

#### 修改现有功能
1. 使用Serena查找相关符号
2. 分析依赖关系
3. 修改实现代码
4. 更新相关测试

### 5. 代码质量检查
- 构建时检查警告和错误
- 遵循命名约定
- 使用适当的设计模式
- 保持代码注释的中文风格

### 6. 版本控制
```powershell
git add .
git commit -m "功能描述"
git push
```

## 调试技巧
- 使用Visual Studio或VS Code的调试功能
- 查看NLog日志输出
- 使用Avalonia的调试工具
- 检查SQLite数据库状态