# 架构详细信息

## 项目层次结构

### Downkyi (主应用程序)
- **类型**: WinExe (Windows可执行文件)
- **职责**: 应用程序入口点，UI展示
- **主要组件**:
  - Program.cs - 应用程序入口
  - App.axaml/App.axaml.cs - Avalonia应用程序配置
  - Views/ - AXAML视图文件
  - ViewModels/ - 视图模型代理类
  - Themes/ - UI主题和样式

### Downkyi.UI (UI层)
- **类型**: 类库
- **职责**: UI逻辑、ViewModels、服务接口
- **主要组件**:
  - ViewModels/ - 业务逻辑的视图模型
  - Services/ - UI相关服务
  - Models/ - UI数据模型
  - Mvvm/ - MVVM基础设施

### Downkyi.Core (核心层)
- **类型**: 类库
- **职责**: 业务逻辑、数据访问、外部API
- **主要组件**:
  - Bili/ - B站API相关功能
  - Downloader/ - 下载器实现
  - Database/ - 数据库访问
  - Settings/ - 配置管理
  - FFmpeg/ - 视频处理
  - Utils/ - 工具类

## 设计模式

### MVVM模式
- **Model**: Downkyi.Core中的业务模型
- **View**: Avalonia AXAML文件
- **ViewModel**: 使用CommunityToolkit.Mvvm实现

### 依赖注入
- 使用Microsoft.Extensions.DependencyInjection
- 服务注册在ServiceLocator中
- 支持接口和实现分离

### 工厂模式
- VideoInfoServiceFactory用于创建不同类型的视频信息服务

## 外部集成

### 下载器集成
- Aria2c: 多线程下载
- 内建下载器: 备用方案

### 视频处理
- FFmpeg: 视频混流、格式转换
- 支持去水印功能

### 数据存储
- SQLite数据库存储用户信息和设置
- 使用SQLCipher加密