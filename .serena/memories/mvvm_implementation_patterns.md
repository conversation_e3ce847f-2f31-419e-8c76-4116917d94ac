# MVVM实现模式

## CommunityToolkit.Mvvm使用模式

### ObservableObject基类
项目中的Model类继承自`ObservableObject`，这是CommunityToolkit.Mvvm提供的基类，自动实现了`INotifyPropertyChanged`接口。

### 属性声明模式
使用`[ObservableProperty]`特性自动生成属性：

```csharp
[ObservableProperty]
private string _name = string.Empty;
```

这会自动生成：
- 公共属性`Name`
- 属性更改通知
- 验证逻辑（如果需要）

### 命令声明模式
使用`[RelayCommand]`特性自动生成命令：

```csharp
[RelayCommand]
private void VisitHomepage() { 
    Process.Start(new ProcessStartInfo(Homepage) { UseShellExecute = true }); 
}
```

这会自动生成：
- `VisitHomepageCommand`属性
- `ICommand`实现
- 命令执行逻辑

### 代码组织
- 使用`#region`组织代码块
- 属性申明区域
- 命令申明区域
- 私有字段使用下划线前缀

## 示例：ThirdParty模型
`ThirdParty`类是一个典型的MVVM模型实现：
- 管理第三方组件信息
- 提供访问主页和许可证的命令
- 使用现代C#特性和MVVM工具包