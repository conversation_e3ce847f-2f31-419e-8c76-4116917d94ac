# 核心业务逻辑

## Downkyi.Core模块结构

### B站API集成 (Bili/)
**接口定义：**
- `ILogin` - 登录接口
- `IUser` - 用户接口  
- `IVideo` - 视频接口

**核心组件：**
- `BiliLocator` - B站定位器
- `Cookies` - Cookie管理
- `Login`, `LoginHelper`, `LoginHelperV2` - 登录实现
- `User`, `Video` - 用户和视频API实现

**数据模型：**
- `NavigationInfo` - 导航信息
- `QRCodeStatus` - 二维码状态
- `Quality` - 视频质量
- `VideoInfo` - 视频信息

**工具类：**
- `BvId` - BV号处理
- `DanmakuSender` - 弹幕发送者
- `ParseEntrance` - 解析入口
- `QualityList` - 质量列表

### 下载器 (Downloader/)
- `MultiThreadDownloader` - 多线程下载器
- `PartialDownloader` - 部分下载器
- 支持文件合并进度事件

### 视频处理 (FFmpeg/)
- `FFmpegHelper` - FFmpeg辅助类
- 视频混流、格式转换、去水印等功能

### 数据存储 (Database/)
- `LoginDatabase` - 登录数据库
- `Cookies`, `Users` - 数据模型
- 使用SQLite存储

### 设置管理 (Settings/)
**设置模型：**
- `AboutSettings` - 关于设置
- `AppSettings` - 应用设置
- `BasicSettings` - 基础设置
- `DanmakuSettings` - 弹幕设置
- `NetworkSettings` - 网络设置
- `UserInfoSettings` - 用户信息设置
- `VideoSettings` - 视频设置
- `VideoContentSettings` - 视频内容设置

**枚举定义：**
- `AfterDownloadOperation` - 下载后操作
- `AllowStatus` - 允许状态
- `DanmakuLayoutAlgorithm` - 弹幕布局算法
- `Downloader` - 下载器类型
- `DownloadFinishedSort` - 下载完成排序
- `OrderFormat` - 排序格式
- `ParseScope` - 解析范围
- `VideoCodecs` - 视频编解码器

**管理器：**
- `SettingsManager` - 设置管理器（分部类实现）

### 工具类 (Utils/)
- `Format` - 格式化工具
- `HardDisk` - 硬盘工具
- `LinkStack`, `Stack` - 数据结构
- `ListHelper` - 列表辅助
- `ObjectHelper` - 对象辅助
- `Encryptor`, `Hash` - 加密工具
- `Number` - 数字验证器

### 文件名处理 (FileName/)
- `FileName` - 文件名处理
- `FileNamePart` - 文件名部分（枚举）
- `HyphenSeparated` - 连字符分隔

### 日志系统 (Logger/)
- `Log` - 日志类
- 使用NLog框架

### 存储管理 (Storage/)
- `Constant` - 常量定义
- `StorageManager` - 存储管理器