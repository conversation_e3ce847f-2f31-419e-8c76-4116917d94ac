# 建议的开发命令

## 环境设置
```powershell
# 刷新环境变量 (安装.NET SDK后)
$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
```

## 构建和运行
```powershell
# 构建整个解决方案
dotnet build

# 构建发布版本
dotnet build -c Release

# 运行应用程序
dotnet run --project Downkyi

# 清理构建输出
dotnet clean
```

## 包管理
```powershell
# 还原NuGet包
dotnet restore

# 添加包引用
dotnet add package <PackageName>

# 移除包引用
dotnet remove package <PackageName>

# 列出包引用
dotnet list package
```

## 项目管理
```powershell
# 添加项目引用
dotnet add reference <ProjectPath>

# 创建新项目
dotnet new <template> -n <ProjectName>
```

## Windows系统工具命令
```powershell
# 文件和目录操作
dir                    # 列出目录内容 (等同于 ls)
cd <path>             # 切换目录
mkdir <dirname>       # 创建目录
rmdir <dirname>       # 删除目录
copy <src> <dest>     # 复制文件
move <src> <dest>     # 移动文件
del <filename>        # 删除文件

# 搜索
findstr <pattern> <files>  # 在文件中搜索文本 (等同于 grep)
where <command>           # 查找命令位置 (等同于 which)

# 进程管理
tasklist              # 列出运行的进程
taskkill /PID <pid>   # 终止进程
```

## Git命令
```powershell
git status            # 查看状态
git add .             # 添加所有更改
git commit -m "message"  # 提交更改
git push              # 推送到远程
git pull              # 拉取远程更改
git branch            # 查看分支
git checkout <branch> # 切换分支
```

## 包管理器 (winget)
```powershell
winget search <package>   # 搜索包
winget install <package>  # 安装包
winget upgrade <package>  # 升级包
winget list              # 列出已安装的包
```