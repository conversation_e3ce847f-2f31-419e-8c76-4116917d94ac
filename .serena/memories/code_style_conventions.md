# 代码风格和约定

## 命名约定
- **类名**: PascalCase (如 `ThirdParty`, `DownloadManager`)
- **方法名**: PascalCase (如 `VisitHomepage`)
- **属性名**: PascalCase
- **私有字段**: 使用下划线前缀 (如 `_name`, `_wait`)
- **命名空间**: 使用项目层次结构 (如 `Downkyi.UI.Models`)

## MVVM模式约定
- 使用 `CommunityToolkit.Mvvm` 的 `ObservableObject` 基类
- 使用 `[ObservableProperty]` 特性自动生成属性
- 使用 `[RelayCommand]` 特性自动生成命令
- ViewModels 继承自 `ViewModelBase`
- 使用依赖注入管理服务

## 文件组织
- Views 使用 `.axaml` 和 `.axaml.cs` 文件
- ViewModels 放在对应的 ViewModels 文件夹中
- Models 放在 Models 文件夹中
- Services 放在 Services 文件夹中

## 代码特性
- 启用 Nullable 引用类型
- 使用 ImplicitUsings
- 目标框架: .NET 8.0
- 使用中文注释和文档

## 项目配置
- Release 配置下禁用调试信息 (`<DebugType>none</DebugType>`)
- 使用 `app.manifest` 配置应用程序清单
- 支持 COM 互操作 (`<BuiltInComInteropSupport>true</BuiltInComInteropSupport>`)