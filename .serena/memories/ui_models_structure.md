# UI模型结构

## Downkyi.UI.Models命名空间

### 模型类列表
- `Cookie` - Cookie信息模型
- `FileNamePartDisplay` - 文件名部分显示模型
- `OrderFormatDisplay` - 排序格式显示模型
- `ParseScopeDisplay` - 解析范围显示模型
- `TabHeader` - 标签页头部模型
- `ThirdParty` - 第三方组件信息模型
- `VideoInfoView` - 视频信息视图模型

### ThirdParty模型详细信息
用于管理第三方组件和依赖项信息：

**属性：**
- `Name` - 组件名称
- `Author` - 作者
- `Version` - 版本号
- `License` - 许可证类型
- `Homepage` - 主页URL
- `LicenseUrl` - 许可证URL

**命令：**
- `VisitHomepageCommand` - 访问主页
- `GetLicenseCommand` - 获取许可证

**使用场景：**
- 关于页面显示第三方组件信息
- 许可证管理
- 依赖项追踪

### 设计特点
- 所有模型都继承自`ObservableObject`
- 使用CommunityToolkit.Mvvm的现代MVVM模式
- 支持数据绑定和命令绑定
- 遵循单一职责原则