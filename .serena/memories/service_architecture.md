# 服务架构

## 服务层组织结构

### 核心服务接口
- `IClipboardService` - 剪贴板服务
- `IDictionaryResource` - 字典资源服务
- `IMainSearchService` - 主搜索服务
- `INavigationService` - 导航服务
- `IStoragePicker` - 存储选择器服务
- `IStorageService` - 存储服务

### 事件服务
- `IBroadcastEvent` / `BroadcastEvent` - 广播事件服务
- `INotificationEvent` / `NotificationEvent` - 通知事件服务

### 视频信息服务
专门处理不同类型的视频信息：
- `IVideoInfoService` - 视频信息服务接口
- `VideoInfoService` - 普通视频信息服务
- `BangumiInfoService` - 番剧信息服务
- `CheeseInfoService` - 课程信息服务
- `IVideoInfoServiceFactory` / `VideoInfoServiceFactory` - 工厂模式实现

### 服务实现
- `MainSearchService` - 主搜索功能实现
- `StorageService` - 存储功能实现

## 设计模式

### 依赖注入
所有服务都通过接口定义，支持依赖注入和测试。

### 工厂模式
`VideoInfoServiceFactory`根据视频类型创建相应的服务实例。

### 事件驱动
使用事件服务实现组件间的松耦合通信。

## 服务职责
- **UI服务**: 处理用户界面相关功能
- **业务服务**: 处理核心业务逻辑
- **基础设施服务**: 提供通用功能支持